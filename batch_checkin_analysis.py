import pandas as pd
from collections import defaultdict
import os

def identify_wave_group(user_nickname, checkin_nickname):
    """识别浪组类型"""
    combined_text = f"{user_nickname or ''} {checkin_nickname or ''}"
    
    if "骇浪" in combined_text:
        return "骇浪"
    elif "核浪" in combined_text:
        return "核浪"
    elif "跃浪" in combined_text:
        return "跃浪"
    else:
        return "无浪组"

def calculate_cumulative_stats(task_counts, max_day):
    """计算累积统计数据"""
    cumulative = {}
    
    # 从最新一天开始向前计算累积人数
    for day in range(max_day, max(0, max_day-5), -1):
        # 第N天的人数 = 完成任务数 >= N 的所有人数
        cumulative[day] = sum(count for task_day, count in task_counts.items() if task_day >= day)
    
    return cumulative

def analyze_file(file_path, file_name):
    """分析单个文件的打卡数据"""
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path, encoding='utf-8')
        print(f"📖 正在处理 {file_name}，共 {len(df)} 条记录")
        
        # 获取最大完成任务数
        max_tasks = df['完成任务数'].max()
        print(f"   最大完成任务数: {max_tasks}")
        
        # 初始化任务完成数统计
        task_stats = {
            "骇浪": defaultdict(int),
            "核浪": defaultdict(int),
            "跃浪": defaultdict(int),
            "总数据": defaultdict(int)
        }
        
        # 统计每个用户的数据
        for _, row in df.iterrows():
            completed_tasks = row['完成任务数']
            wave_group = identify_wave_group(row['用户昵称'], row['打卡昵称'])
            
            # 统计总数据
            task_stats["总数据"][completed_tasks] += 1
            
            # 统计浪组数据（排除无浪组）
            if wave_group != "无浪组":
                task_stats[wave_group][completed_tasks] += 1
        
        # 计算累积统计
        cumulative_stats = {}
        for group, counts in task_stats.items():
            cumulative_stats[group] = calculate_cumulative_stats(counts, max_tasks)
        
        return {
            "文件名": file_name,
            "最新一天": max_tasks,
            "累积统计": cumulative_stats,
            "处理状态": "成功"
        }
        
    except Exception as e:
        print(f"❌ 处理 {file_name} 时出错: {e}")
        return {
            "文件名": file_name,
            "处理状态": "失败",
            "错误信息": str(e)
        }

def format_output(results):
    """格式化输出结果"""
    print("\n" + "="*70)
    print("🌊 浪前打卡数据累积统计报告")
    print("="*70)
    
    for result in results:
        if result["处理状态"] == "失败":
            print(f"\n❌ {result['文件名']} 处理失败: {result['错误信息']}")
            continue
            
        print(f"\n{'='*50}")
        print(f"📊 {result['文件名']} 累积打卡统计")
        print(f"{'='*50}")
        print(f"最新一天：第{result['最新一天']}天")
        
        # 计算显示的天数范围
        latest_day = result['最新一天']
        target_days = list(range(latest_day, max(0, latest_day-5), -1))
        
        for group_name, group_data in result['累积统计'].items():
            print(f"\n🌊 {group_name}:")
            for day in target_days:
                count = group_data.get(day, 0)
                if day == latest_day:
                    print(f"  第{day}天（最新）: {count}人")
                else:
                    print(f"  第{day}天及以上: {count}人")

def main():
    """主函数：批量处理所有文件"""
    # 定义要处理的文件
    files_to_analyze = [
        ("2025-08-07-《🌊浪前·选打【阅读打卡】》-打卡学员数据8797.csv", "阅读打卡"),
        ("2025-08-07-《🌊浪前·必打【课程打卡】》-打卡学员数据3693.csv", "必打课程"),
        ("2025-08-07-《🌊浪前·选打【破晓打卡】》-打卡学员数据3497.csv", "破晓打卡")
    ]
    
    print("🚀 开始批量处理打卡数据...")
    results = []
    
    for file_path, file_name in files_to_analyze:
        if os.path.exists(file_path):
            result = analyze_file(file_path, file_name)
            results.append(result)
        else:
            print(f"⚠️  文件不存在: {file_path}")
            results.append({
                "文件名": file_name,
                "处理状态": "失败",
                "错误信息": "文件不存在"
            })
    
    # 输出统计结果
    format_output(results)
    
    # 生成汇总信息
    successful_files = [r for r in results if r["处理状态"] == "成功"]
    print(f"\n📈 处理完成：成功 {len(successful_files)} 个文件，失败 {len(results) - len(successful_files)} 个文件")

if __name__ == "__main__":
    main()