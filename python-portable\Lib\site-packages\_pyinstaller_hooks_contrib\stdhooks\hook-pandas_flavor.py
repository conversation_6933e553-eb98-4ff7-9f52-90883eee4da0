# ------------------------------------------------------------------
# Copyright (c) 2022 PyInstaller Development Team.
#
# This file is distributed under the terms of the GNU General Public
# License (version 2.0 or later).
#
# The full license is available in LICENSE, distributed with
# this software.
#
# SPDX-License-Identifier: GPL-2.0-or-later
# ------------------------------------------------------------------

from PyInstaller.utils.hooks import is_module_satisfies

# As of version 0.3.0, pandas_flavor uses lazy loader to import `register` and `xarray` sub-modules. In earlier
# versions, these used to be imported directly. This was removed in 0.7.0.
if is_module_satisfies("pandas_flavor >= 0.3.0, < 0.7.0"):
    hiddenimports = ['pandas_flavor.register', 'pandas_flavor.xarray']
