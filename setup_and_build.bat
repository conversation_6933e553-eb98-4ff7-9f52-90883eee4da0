@echo off
echo ========================================
echo Checkin Data Analysis Tool - Build Script
echo ========================================

echo Checking Python environment...
python --version
if %errorlevel% neq 0 (
    echo Error: Python not found, please install Python first
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
echo Using Aliyun mirror for faster download...
pip install -i https://mirrors.aliyun.com/pypi/simple/ --timeout 300 --retries 3 pandas matplotlib numpy tkinterdnd2 pyinstaller --trusted-host mirrors.aliyun.com

if %errorlevel% neq 0 (
    echo Dependency installation failed, please check network connection
    pause
    exit /b 1
)

echo.
echo Building standalone exe file...
echo This may take several minutes, please wait...

pyinstaller --onefile --windowed --name="浪前打卡数据分析工具" --icon=icon.ico --add-data="requirements.txt;." --hidden-import=tkinterdnd2 --hidden-import=matplotlib.backends.backend_tkagg --hidden-import=matplotlib.backends.backend_pdf --hidden-import=matplotlib.backends.backend_svg --hidden-import=pandas._libs.tslibs.timedeltas --hidden-import=pandas._libs.tslibs.np_datetime --hidden-import=pandas._libs.tslibs.nattype --hidden-import=pandas._libs.window.indexers --collect-all=matplotlib --collect-all=pandas --collect-all=numpy --noconsole checkin_analyzer_gui.py

if %errorlevel% neq 0 (
    echo Build failed, please check error messages
    pause
    exit /b 1
)

echo.
echo Build successful!
echo exe file location: dist\浪前打卡数据分析工具.exe
echo.
echo Usage instructions:
echo 1. Copy dist\浪前打卡数据分析工具.exe to any computer
echo 2. No Python environment required, double-click to run
echo 3. Drag CSV files for analysis
echo.
pause