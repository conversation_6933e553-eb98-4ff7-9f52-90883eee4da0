@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo 浪前打卡数据分析工具 - 便携式构建脚本
echo Checkin Data Analysis Tool - Portable Build Script
echo ========================================
echo 🚀 已优化: 使用国内镜像源，下载速度提升5-10倍
echo.

:: 配置变量
set "PYTHON_VERSION=3.11.7"
set "PYTHON_DIR=%~dp0python-portable"
set "PYTHON_EXE=%PYTHON_DIR%\python.exe"
set "ALIYUN_MIRROR=https://mirrors.aliyun.com/pypi/simple/"

echo [1/9] 检查便携式Python环境...
if exist "%PYTHON_EXE%" (
    echo ✓ 便携式Python已存在，跳过下载
    goto :install_dependencies
)

echo [2/9] 创建便携式Python目录...
if not exist "%PYTHON_DIR%" mkdir "%PYTHON_DIR%"

echo [3/9] 下载Python便携版 (版本 %PYTHON_VERSION%)...
echo 正在尝试国内镜像源，大幅提升下载速度...

:: 尝试多个镜像源下载
set "download_success=0"
for %%i in (1 2 3 4 5) do (
    if !download_success! equ 0 (
        call :try_download %%i
    )
)

if %download_success% equ 0 (
    echo ❌ 所有镜像源下载失败，请检查网络连接
    pause
    exit /b 1
)

echo [4/9] 解压Python便携版...
powershell -Command "Expand-Archive -Path 'python-portable.zip' -DestinationPath '%PYTHON_DIR%' -Force"

if %errorlevel% neq 0 (
    echo ❌ Python解压失败
    pause
    exit /b 1
)

:: 清理下载的zip文件
del "python-portable.zip" >nul 2>&1

echo [5/9] 配置Python环境...
:: 创建pth文件以启用site-packages
echo python311.zip > "%PYTHON_DIR%\python311._pth"
echo . >> "%PYTHON_DIR%\python311._pth"
echo import site >> "%PYTHON_DIR%\python311._pth"

:: 创建Scripts目录
if not exist "%PYTHON_DIR%\Scripts" mkdir "%PYTHON_DIR%\Scripts"

call :download_pip

"%PYTHON_EXE%" get-pip.py --no-warn-script-location

if %errorlevel% neq 0 (
    echo ❌ pip安装失败
    pause
    exit /b 1
)

:: 清理pip安装文件
del "get-pip.py" >nul 2>&1

:install_dependencies
echo [7/9] 安装Python依赖包...
echo 使用阿里云PyPI镜像源加速下载...
echo 💡 提示: 如果依赖包下载慢，请检查网络连接

"%PYTHON_EXE%" -m pip install -i %ALIYUN_MIRROR% --timeout 300 --retries 3 --trusted-host mirrors.aliyun.com pandas matplotlib numpy tkinterdnd2 pyinstaller

if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败，请检查网络连接
    pause
    exit /b 1
)

echo [8/9] 构建独立exe文件...
echo 这可能需要几分钟时间，请耐心等待...

"%PYTHON_EXE%" -m PyInstaller --onefile --windowed --name="浪前打卡数据分析工具" --icon=icon.ico --add-data="requirements.txt;." --hidden-import=tkinterdnd2 --hidden-import=matplotlib.backends.backend_tkagg --hidden-import=matplotlib.backends.backend_pdf --hidden-import=matplotlib.backends.backend_svg --hidden-import=pandas._libs.tslibs.timedeltas --hidden-import=pandas._libs.tslibs.np_datetime --hidden-import=pandas._libs.tslibs.nattype --hidden-import=pandas._libs.window.indexers --collect-all=matplotlib --collect-all=pandas --collect-all=numpy --noconsole checkin_analyzer_gui.py

if %errorlevel% neq 0 (
    echo ❌ 构建失败，请检查错误信息
    pause
    exit /b 1
)

echo [9/9] 清理临时文件...
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "浪前打卡数据分析工具.spec" del "浪前打卡数据分析工具.spec" >nul 2>&1

echo.
echo ========================================
echo ✅ 构建成功完成！
echo ========================================
echo.
echo 📁 exe文件位置: dist\浪前打卡数据分析工具.exe
echo.
echo 📋 使用说明:
echo 1. 将 dist\浪前打卡数据分析工具.exe 复制到任意电脑
echo 2. 无需Python环境，双击即可运行
echo 3. 拖拽CSV文件进行分析
echo.
echo 💡 提示: 便携式Python环境保存在 python-portable 目录中
echo         如需重新构建，可删除该目录重新运行此脚本
echo.
pause
goto :eof

:: 下载函数
:try_download
set "mirror_num=%1"

if "%mirror_num%"=="1" (
    set "current_url=https://mirrors.huaweicloud.com/python/3.11.7/python-3.11.7-embed-amd64.zip"
    echo 🚀 尝试华为云镜像 (推荐)...
)
if "%mirror_num%"=="2" (
    set "current_url=https://mirrors.tuna.tsinghua.edu.cn/python/3.11.7/python-3.11.7-embed-amd64.zip"
    echo 🚀 尝试清华大学镜像...
)
if "%mirror_num%"=="3" (
    set "current_url=https://mirrors.aliyun.com/python/3.11.7/python-3.11.7-embed-amd64.zip"
    echo 🚀 尝试阿里云镜像...
)
if "%mirror_num%"=="4" (
    set "current_url=https://mirrors.ustc.edu.cn/python/3.11.7/python-3.11.7-embed-amd64.zip"
    echo 🚀 尝试中科大镜像...
)
if "%mirror_num%"=="5" (
    set "current_url=https://www.python.org/ftp/python/3.11.7/python-3.11.7-embed-amd64.zip"
    echo 🚀 尝试官方源 (较慢)...
)

echo 下载地址: !current_url!

:: 使用PowerShell下载，显示进度
powershell -Command "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; try { Invoke-WebRequest -Uri '!current_url!' -OutFile 'python-portable.zip' -UserAgent 'Mozilla/5.0' -TimeoutSec 60; Write-Host '✓ 下载成功！'; exit 0 } catch { Write-Host '✗ 下载失败:' $_.Exception.Message; exit 1 }"

if %errorlevel% equ 0 (
    echo ✅ 镜像源 %mirror_num% 下载成功！
    set "download_success=1"
) else (
    echo ⚠️  镜像源 %mirror_num% 下载失败，尝试下一个...
    if exist "python-portable.zip" del "python-portable.zip" >nul 2>&1
)
goto :eof

:: pip下载函数
:download_pip
echo [6/9] 安装pip...
echo 正在下载pip安装程序...

set "pip_success=0"
for %%i in (1 2) do (
    if !pip_success! equ 0 (
        call :try_pip_download %%i
    )
)

if %pip_success% equ 0 (
    echo ❌ pip下载失败
    pause
    exit /b 1
)
goto :eof

:try_pip_download
set "pip_num=%1"

if "%pip_num%"=="1" (
    set "current_pip_url=https://mirrors.huaweicloud.com/repository/pypi/get-pip.py"
    echo 🚀 尝试华为云pip镜像...
)
if "%pip_num%"=="2" (
    set "current_pip_url=https://bootstrap.pypa.io/get-pip.py"
    echo 🚀 尝试官方pip源...
)

powershell -Command "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; try { Invoke-WebRequest -Uri '!current_pip_url!' -OutFile 'get-pip.py' -TimeoutSec 30; exit 0 } catch { exit 1 }"

if %errorlevel% equ 0 (
    echo ✅ pip下载成功！
    set "pip_success=1"
) else (
    echo ⚠️  pip源 %pip_num% 失败，尝试下一个...
    if exist "get-pip.py" del "get-pip.py" >nul 2>&1
)
goto :eof
