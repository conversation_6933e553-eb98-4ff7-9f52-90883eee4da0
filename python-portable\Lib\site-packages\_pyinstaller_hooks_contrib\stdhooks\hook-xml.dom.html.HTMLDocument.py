# ------------------------------------------------------------------
# Copyright (c) 2020 PyInstaller Development Team.
#
# This file is distributed under the terms of the GNU General Public
# License (version 2.0 or later).
#
# The full license is available in LICENSE, distributed with
# this software.
#
# SPDX-License-Identifier: GPL-2.0-or-later
# ------------------------------------------------------------------

# xml.dom.html.HTMLDocument
hiddenimports = ['xml.dom.html.HTMLAnchorElement',
                 'xml.dom.html.HTMLAppletElement',
                 'xml.dom.html.HTMLAreaElement',
                 'xml.dom.html.HTMLBaseElement',
                 'xml.dom.html.HTMLBaseFontElement',
                 'xml.dom.html.HTMLBodyElement',
                 'xml.dom.html.HTMLBRElement',
                 'xml.dom.html.HTMLButtonElement',
                 'xml.dom.html.HTMLDirectoryElement',
                 'xml.dom.html.HTMLDivElement',
                 'xml.dom.html.HTMLDListElement',
                 'xml.dom.html.HTMLElement',
                 'xml.dom.html.HTMLFieldSetElement',
                 'xml.dom.html.HTMLFontElement',
                 'xml.dom.html.HTMLFormElement',
                 'xml.dom.html.HTMLFrameElement',
                 'xml.dom.html.HTMLFrameSetElement',
                 'xml.dom.html.HTMLHeadElement',
                 'xml.dom.html.HTMLHeadingElement',
                 'xml.dom.html.HTMLHRElement',
                 'xml.dom.html.HTMLHtmlElement',
                 'xml.dom.html.HTMLIFrameElement',
                 'xml.dom.html.HTMLImageElement',
                 'xml.dom.html.HTMLInputElement',
                 'xml.dom.html.HTMLIsIndexElement',
                 'xml.dom.html.HTMLLabelElement',
                 'xml.dom.html.HTMLLegendElement',
                 'xml.dom.html.HTMLLIElement',
                 'xml.dom.html.HTMLLinkElement',
                 'xml.dom.html.HTMLMapElement',
                 'xml.dom.html.HTMLMenuElement',
                 'xml.dom.html.HTMLMetaElement',
                 'xml.dom.html.HTMLModElement',
                 'xml.dom.html.HTMLObjectElement',
                 'xml.dom.html.HTMLOListElement',
                 'xml.dom.html.HTMLOptGroupElement',
                 'xml.dom.html.HTMLOptionElement',
                 'xml.dom.html.HTMLParagraphElement',
                 'xml.dom.html.HTMLParamElement',
                 'xml.dom.html.HTMLPreElement',
                 'xml.dom.html.HTMLQuoteElement',
                 'xml.dom.html.HTMLScriptElement',
                 'xml.dom.html.HTMLSelectElement',
                 'xml.dom.html.HTMLStyleElement',
                 'xml.dom.html.HTMLTableCaptionElement',
                 'xml.dom.html.HTMLTableCellElement',
                 'xml.dom.html.HTMLTableColElement',
                 'xml.dom.html.HTMLTableElement',
                 'xml.dom.html.HTMLTableRowElement',
                 'xml.dom.html.HTMLTableSectionElement',
                 'xml.dom.html.HTMLTextAreaElement',
                 'xml.dom.html.HTMLTitleElement',
                 'xml.dom.html.HTMLUListElement',
                 ]
