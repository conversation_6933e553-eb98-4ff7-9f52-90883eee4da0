@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo 浪前打卡数据分析工具 - 强化版构建脚本
echo ========================================
echo 自动处理Python安装和路径问题
echo.

:: 配置变量
set "PYTHON_DIR=%~dp0python-portable"
set "PYTHON_EXE="

echo [1/8] 检查现有Python安装包...
if exist "python-3.11.7-amd64.exe" (
    echo [OK] 发现Python安装包
) else (
    echo [ERROR] 未找到python-3.11.7-amd64.exe文件
    echo 请下载Python 3.11.7安装包到当前目录
    pause
    exit /b 1
)

echo [2/8] 清理旧安装并重新安装Python...
if exist "%PYTHON_DIR%" (
    echo 删除旧的Python目录...
    rmdir /s /q "%PYTHON_DIR%" >nul 2>&1
)

echo 创建安装目录...
mkdir "%PYTHON_DIR%" >nul 2>&1

echo 正在安装Python（详细模式）...
echo 安装命令: python-3.11.7-amd64.exe /quiet InstallAllUsers=0 TargetDir="%PYTHON_DIR%" PrependPath=0 Include_test=0 Include_launcher=0

python-3.11.7-amd64.exe /quiet InstallAllUsers=0 TargetDir="%PYTHON_DIR%" PrependPath=0 Include_test=0 Include_launcher=0

echo 等待安装完成...
timeout /t 10 /nobreak >nul

echo [3/8] 全面搜索Python安装位置...
echo 检查预期位置: %PYTHON_DIR%\python.exe
if exist "%PYTHON_DIR%\python.exe" (
    set "PYTHON_EXE=%PYTHON_DIR%\python.exe"
    echo ✓ 找到Python: !PYTHON_EXE!
    goto :found_python
)

echo 搜索整个python-portable目录...
for /f "delims=" %%i in ('dir /s /b "%PYTHON_DIR%\python.exe" 2^>nul') do (
    set "PYTHON_EXE=%%i"
    echo ✓ 找到Python: !PYTHON_EXE!
    goto :found_python
)

echo 检查系统Python...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set "PYTHON_EXE=python"
    echo ✓ 使用系统Python
    goto :found_python
)

echo [ERROR] 无法找到任何Python安装
echo 请检查安装是否成功
pause
exit /b 1

:found_python
echo [4/8] 验证Python和tkinter...
echo 测试Python版本...
"!PYTHON_EXE!" --version

echo 测试tkinter模块...
"!PYTHON_EXE!" -c "import tkinter; print('✓ tkinter模块可用')"

if %errorlevel% neq 0 (
    echo [WARNING] tkinter模块不可用，尝试安装tk...
    "!PYTHON_EXE!" -m pip install tk
)

echo [5/8] 升级pip...
"!PYTHON_EXE!" -m pip install --upgrade pip

echo [6/8] 安装依赖包（使用清华镜像）...
set "PIP_INDEX=-i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn"

echo 安装numpy...
"!PYTHON_EXE!" -m pip install numpy %PIP_INDEX%

echo 安装pandas...
"!PYTHON_EXE!" -m pip install pandas %PIP_INDEX%

echo 安装matplotlib...
"!PYTHON_EXE!" -m pip install matplotlib %PIP_INDEX%

echo 安装tkinterdnd2...
"!PYTHON_EXE!" -m pip install tkinterdnd2 %PIP_INDEX%

echo 安装pyinstaller...
"!PYTHON_EXE!" -m pip install pyinstaller %PIP_INDEX%

echo [7/8] 最终模块测试...
"!PYTHON_EXE!" -c "import tkinter, pandas, matplotlib, numpy, tkinterdnd2; print('✓ 所有模块可用')"

if %errorlevel% neq 0 (
    echo [ERROR] 模块测试失败
    echo 尝试显示详细错误...
    "!PYTHON_EXE!" -c "import tkinter"
    "!PYTHON_EXE!" -c "import pandas"
    "!PYTHON_EXE!" -c "import matplotlib"
    "!PYTHON_EXE!" -c "import tkinterdnd2"
    pause
    exit /b 1
)

echo [8/8] 构建exe文件...
echo 正在使用PyInstaller打包...

"!PYTHON_EXE!" -m PyInstaller --onefile --windowed --name="浪前打卡数据分析工具" --hidden-import=tkinter --hidden-import=tkinter.ttk --hidden-import=tkinter.messagebox --hidden-import=tkinter.filedialog --hidden-import=tkinterdnd2 --hidden-import=matplotlib.backends.backend_tkagg --collect-all=matplotlib --collect-all=pandas --noconsole checkin_analyzer_gui.py

if %errorlevel% neq 0 (
    echo [ERROR] PyInstaller构建失败
    echo 尝试简化版本...
    "!PYTHON_EXE!" -m PyInstaller --onefile --name="浪前打卡数据分析工具" checkin_analyzer_gui.py
    if %errorlevel% neq 0 (
        echo [ERROR] 简化构建也失败
        pause
        exit /b 1
    )
)

:: 清理
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "浪前打卡数据分析工具.spec" del "浪前打卡数据分析工具.spec" >nul 2>&1

echo.
echo ========================================
echo ✅ 构建完成！
echo ========================================
echo.
echo 📁 exe文件: dist\浪前打卡数据分析工具.exe
echo 🐍 Python路径: !PYTHON_EXE!
echo.
echo 请测试exe文件是否正常运行
pause
