@echo off
chcp 65001 >nul
echo ========================================
echo 构建流程测试脚本
echo Build Process Test Script
echo ========================================
echo.

echo 测试1: 检查必要文件是否存在...
if not exist "checkin_analyzer_gui.py" (
    echo ❌ 缺少主程序文件: checkin_analyzer_gui.py
    goto :test_failed
)

if not exist "requirements.txt" (
    echo ❌ 缺少依赖文件: requirements.txt
    goto :test_failed
)

echo ✓ 必要文件检查通过

echo.
echo 测试2: 检查网络连接...
ping -n 1 www.python.org >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  警告: 无法连接到Python官网，可能影响下载
) else (
    echo ✓ 网络连接正常
)

echo.
echo 测试3: 检查PowerShell可用性...
powershell -Command "Write-Host 'PowerShell测试成功'" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PowerShell不可用，构建可能失败
    goto :test_failed
) else (
    echo ✓ PowerShell可用
)

echo.
echo 测试4: 检查磁盘空间...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set free_space=%%a
echo 可用磁盘空间: %free_space% 字节

echo.
echo ========================================
echo ✅ 所有测试通过！
echo 可以安全运行 setup_and_build.bat
echo ========================================
pause
exit /b 0

:test_failed
echo.
echo ========================================
echo ❌ 测试失败！
echo 请解决上述问题后重新测试
echo ========================================
pause
exit /b 1
