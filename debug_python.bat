@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo Python安装诊断工具
echo ========================================
echo.

set "PYTHON_DIR=%~dp0python-portable"

echo 正在检查Python安装情况...
echo.

echo 1. 检查python-portable目录:
if exist "%PYTHON_DIR%" (
    echo ✓ python-portable目录存在
    dir "%PYTHON_DIR%" /b
) else (
    echo ✗ python-portable目录不存在
)

echo.
echo 2. 搜索Python可执行文件:
if exist "%PYTHON_DIR%\python.exe" (
    echo ✓ 找到: %PYTHON_DIR%\python.exe
    set "PYTHON_EXE=%PYTHON_DIR%\python.exe"
) else (
    echo ✗ 未找到: %PYTHON_DIR%\python.exe
    
    echo 搜索其他位置...
    for /f "delims=" %%i in ('dir /s /b "%PYTHON_DIR%\python.exe" 2^>nul') do (
        echo ✓ 找到: %%i
        set "PYTHON_EXE=%%i"
        goto :found
    )
    echo ✗ 未找到任何python.exe文件
    goto :end
)

:found
echo.
echo 3. 测试Python版本:
"!PYTHON_EXE!" --version

echo.
echo 4. 测试tkinter模块:
"!PYTHON_EXE!" -c "import tkinter; print('✓ tkinter可用')" 2>nul
if %errorlevel% neq 0 (
    echo ✗ tkinter不可用
    echo 尝试详细错误信息:
    "!PYTHON_EXE!" -c "import tkinter"
) else (
    echo ✓ tkinter测试成功
)

echo.
echo 5. 测试其他模块:
"!PYTHON_EXE!" -c "import sys; print('Python路径:', sys.executable)"
"!PYTHON_EXE!" -c "import sys; print('模块搜索路径:'); [print('  ', p) for p in sys.path]"

echo.
echo 6. 检查pip:
"!PYTHON_EXE!" -m pip --version

:end
echo.
echo 诊断完成！
pause
