@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo 浪前打卡数据分析工具 - 修复版构建脚本
echo ========================================
echo 解决tkinter缺失和乱码问题
echo.

:: 配置变量
set "PYTHON_DIR=%~dp0python-portable"
set "PYTHON_EXE=%PYTHON_DIR%\python.exe"

echo [1/7] 检查现有Python安装包...
if exist "python-3.11.7-amd64.exe" (
    echo [OK] 发现Python安装包，将使用本地文件
    goto :install_python
)

echo [ERROR] 未找到python-3.11.7-amd64.exe文件
echo 请确保Python安装包在当前目录中
echo 您可以从以下地址下载:
echo https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe
echo 或使用华为云镜像:
echo https://mirrors.huaweicloud.com/python/3.11.7/python-3.11.7-amd64.exe
pause
exit /b 1

:install_python
echo [2/7] 安装Python到便携目录...
if exist "%PYTHON_DIR%" (
    echo 删除旧的Python目录...
    rmdir /s /q "%PYTHON_DIR%"
)

echo 正在安装Python，请稍候...
python-3.11.7-amd64.exe /quiet InstallAllUsers=0 TargetDir="%PYTHON_DIR%" PrependPath=0 Include_test=0 Include_launcher=0

if %errorlevel% neq 0 (
    echo [ERROR] Python安装失败
    pause
    exit /b 1
)

echo [3/7] 验证Python和tkinter...
"%PYTHON_EXE%" -c "import tkinter; print('✓ tkinter模块可用')"

if %errorlevel% neq 0 (
    echo [ERROR] tkinter模块验证失败
    pause
    exit /b 1
)

echo [4/7] 升级pip并配置镜像源...
"%PYTHON_EXE%" -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn

echo [5/7] 安装依赖包...
echo 正在安装pandas...
"%PYTHON_EXE%" -m pip install pandas -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn

echo 正在安装matplotlib...
"%PYTHON_EXE%" -m pip install matplotlib -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn

echo 正在安装numpy...
"%PYTHON_EXE%" -m pip install numpy -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn

echo 正在安装tkinterdnd2...
"%PYTHON_EXE%" -m pip install tkinterdnd2 -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn

echo 正在安装pyinstaller...
"%PYTHON_EXE%" -m pip install pyinstaller -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn

echo [6/7] 测试程序运行...
echo 正在测试主程序是否可以正常导入模块...
"%PYTHON_EXE%" -c "import tkinter; import pandas; import matplotlib; import tkinterdnd2; print('✓ 所有模块导入成功')"

if %errorlevel% neq 0 (
    echo [ERROR] 模块导入测试失败
    pause
    exit /b 1
)

echo [7/7] 构建exe文件...
echo 正在打包，这可能需要几分钟...

"%PYTHON_EXE%" -m PyInstaller --onefile --windowed --name="浪前打卡数据分析工具" --hidden-import=tkinter --hidden-import=tkinter.ttk --hidden-import=tkinter.messagebox --hidden-import=tkinter.filedialog --hidden-import=tkinterdnd2 --hidden-import=matplotlib.backends.backend_tkagg --hidden-import=pandas --hidden-import=numpy --collect-all=matplotlib --collect-all=pandas --noconsole checkin_analyzer_gui.py

if %errorlevel% neq 0 (
    echo [ERROR] 构建失败，查看上方错误信息
    pause
    exit /b 1
)

:: 清理临时文件
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "浪前打卡数据分析工具.spec" del "浪前打卡数据分析工具.spec" >nul 2>&1

echo.
echo ========================================
echo ✅ 构建成功完成！
echo ========================================
echo.
echo 📁 exe文件位置: dist\浪前打卡数据分析工具.exe
echo.
echo 📋 使用说明:
echo 1. 将exe文件复制到任意电脑
echo 2. 无需Python环境，双击即可运行
echo 3. 拖拽CSV文件进行分析
echo.
echo 💡 提示: 如果运行时出现问题，请检查:
echo    - 是否有杀毒软件误报
echo    - 是否有足够的磁盘空间
echo    - 是否有管理员权限
echo.
pause
