import PyInstaller.__main__
import os
import sys

def build_exe():
    """构建exe文件"""
    
    # PyInstaller参数
    args = [
        'checkin_analyzer_gui.py',
        '--onefile',
        '--windowed',
        '--name=浪前打卡数据分析工具',
        '--icon=icon.ico',  # 如果有图标文件
        '--add-data=requirements.txt;.',
        '--hidden-import=tkinterdnd2',
        '--hidden-import=matplotlib.backends.backend_tkagg',
        '--collect-all=matplotlib',
        '--collect-all=pandas',
        '--noconsole'
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists('icon.ico'):
        args = [arg for arg in args if not arg.startswith('--icon')]
    
    print("开始构建exe文件...")
    PyInstaller.__main__.run(args)
    print("构建完成！exe文件位于 dist/ 目录中")

if __name__ == "__main__":
    build_exe()