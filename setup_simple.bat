@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 浪前打卡数据分析工具 - 便携式构建脚本
echo ========================================
echo.

:: 配置变量
set "PYTHON_VERSION=3.11.7"
set "PYTHON_DIR=%~dp0python-portable"
set "PYTHON_EXE=%PYTHON_DIR%\python.exe"

echo [1/9] 检查便携式Python环境...
if exist "%PYTHON_EXE%" (
    echo [OK] 便携式Python已存在，跳过下载
    goto :install_dependencies
)

echo [2/9] 创建便携式Python目录...
if not exist "%PYTHON_DIR%" mkdir "%PYTHON_DIR%"

echo [3/9] 下载Python便携版...
echo 正在尝试华为云镜像...

:: 使用华为云镜像下载
set "PYTHON_URL=https://mirrors.huaweicloud.com/python/3.11.7/python-3.11.7-embed-amd64.zip"
echo 下载地址: %PYTHON_URL%

powershell -Command "try { Invoke-WebRequest -Uri '%PYTHON_URL%' -OutFile 'python-portable.zip' -TimeoutSec 60; Write-Host '[OK] 下载成功'; exit 0 } catch { Write-Host '[ERROR] 下载失败'; exit 1 }"

if %errorlevel% neq 0 (
    echo [ERROR] Python下载失败，请检查网络连接
    pause
    exit /b 1
)

echo [4/9] 解压Python便携版...
powershell -Command "Expand-Archive -Path 'python-portable.zip' -DestinationPath '%PYTHON_DIR%' -Force"

if %errorlevel% neq 0 (
    echo [ERROR] Python解压失败
    pause
    exit /b 1
)

del "python-portable.zip" >nul 2>&1

echo [5/9] 配置Python环境...
echo python311.zip > "%PYTHON_DIR%\python311._pth"
echo . >> "%PYTHON_DIR%\python311._pth"
echo import site >> "%PYTHON_DIR%\python311._pth"

if not exist "%PYTHON_DIR%\Scripts" mkdir "%PYTHON_DIR%\Scripts"

echo [6/9] 安装pip...
powershell -Command "try { Invoke-WebRequest -Uri 'https://bootstrap.pypa.io/get-pip.py' -OutFile 'get-pip.py' -TimeoutSec 30; exit 0 } catch { exit 1 }"

if %errorlevel% neq 0 (
    echo [ERROR] pip下载失败
    pause
    exit /b 1
)

"%PYTHON_EXE%" get-pip.py --no-warn-script-location

if %errorlevel% neq 0 (
    echo [ERROR] pip安装失败
    pause
    exit /b 1
)

del "get-pip.py" >nul 2>&1

:install_dependencies
echo [7/9] 安装Python依赖包...
echo 使用阿里云镜像源...

"%PYTHON_EXE%" -m pip install -i https://mirrors.aliyun.com/pypi/simple/ --timeout 300 --retries 3 --trusted-host mirrors.aliyun.com pandas matplotlib numpy tkinterdnd2 pyinstaller

if %errorlevel% neq 0 (
    echo [ERROR] 依赖包安装失败
    pause
    exit /b 1
)

echo [8/9] 构建独立exe文件...
echo 这可能需要几分钟时间，请耐心等待...

"%PYTHON_EXE%" -m PyInstaller --onefile --windowed --name="浪前打卡数据分析工具" --noconsole checkin_analyzer_gui.py

if %errorlevel% neq 0 (
    echo [ERROR] 构建失败
    pause
    exit /b 1
)

echo [9/9] 清理临时文件...
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "浪前打卡数据分析工具.spec" del "浪前打卡数据分析工具.spec" >nul 2>&1

echo.
echo ========================================
echo [SUCCESS] 构建成功完成！
echo ========================================
echo.
echo exe文件位置: dist\浪前打卡数据分析工具.exe
echo.
echo 使用说明:
echo 1. 将exe文件复制到任意电脑
echo 2. 无需Python环境，双击即可运行
echo 3. 拖拽CSV文件进行分析
echo.
pause
