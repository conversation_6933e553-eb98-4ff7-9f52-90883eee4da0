import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinterdnd2 as tkdnd
import pandas as pd
from collections import defaultdict
import os
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CheckinAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🌊 浪前打卡数据分析工具")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # 文件信息存储
        self.files_info = []  # 存储文件路径和识别的类型
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=10)
        
        title_label = tk.Label(title_frame, text="🌊 浪前打卡数据分析工具", 
                              font=('Arial', 18, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        title_label.pack()
        
        # 文件拖拽区域
        self.create_drop_zone()
        
        # 文件列表显示
        self.create_file_list()
        
        # 控制按钮
        control_frame = tk.Frame(self.root, bg='#f0f0f0')
        control_frame.pack(pady=10)
        
        self.analyze_btn = tk.Button(control_frame, text="🚀 开始分析", 
                                   command=self.start_analysis,
                                   font=('Arial', 12, 'bold'),
                                   bg='#3498db', fg='white',
                                   padx=20, pady=10)
        self.analyze_btn.pack(side='left', padx=10)
        
        self.clear_btn = tk.Button(control_frame, text="🗑️ 清空文件", 
                                 command=self.clear_files,
                                 font=('Arial', 12),
                                 bg='#e74c3c', fg='white',
                                 padx=20, pady=10)
        self.clear_btn.pack(side='left', padx=10)
        
        # 进度条
        self.progress_var = tk.StringVar(value="准备就绪")
        progress_frame = tk.Frame(self.root, bg='#f0f0f0')
        progress_frame.pack(pady=10, padx=20, fill='x')
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill='x', pady=5)
        
        self.status_label = tk.Label(progress_frame, textvariable=self.progress_var,
                                   bg='#f0f0f0', font=('Arial', 10))
        self.status_label.pack()
        
        # 结果显示区域
        self.create_result_area()
        
    def create_drop_zone(self):
        """创建单个文件拖拽区域"""
        drop_frame = tk.Frame(self.root, bg='#f0f0f0')
        drop_frame.pack(pady=20, padx=20, fill='x')
        
        # 拖拽区域 - 修复relief参数
        self.drop_zone = tk.Frame(drop_frame, bg='#e8f4fd', relief='ridge', bd=2)
        self.drop_zone.pack(fill='x', pady=10)
        
        # 拖拽提示
        drop_label = tk.Label(self.drop_zone, 
                            text="📁 拖拽CSV文件到此处或点击选择文件\n\n支持多文件选择，程序将自动识别文件类型：\n• 阅读打卡文件\n• 课程打卡文件（必打）\n• 破晓打卡文件",
                            font=('Arial', 12),
                            bg='#e8f4fd', fg='#2c3e50',
                            pady=30)
        drop_label.pack(fill='x')
        
        # 绑定事件
        drop_label.bind("<Button-1>", self.select_files)
        self.drop_zone.bind("<Button-1>", self.select_files)
        
        # 启用拖拽功能
        try:
            drop_label.drop_target_register(tkdnd.DND_FILES)
            drop_label.dnd_bind('<<Drop>>', self.on_drop)
            self.drop_zone.drop_target_register(tkdnd.DND_FILES)
            self.drop_zone.dnd_bind('<<Drop>>', self.on_drop)
        except:
            pass
    
    def create_file_list(self):
        """创建文件列表显示区域"""
        list_frame = tk.Frame(self.root, bg='#f0f0f0')
        list_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        # 列表标题
        list_title = tk.Label(list_frame, text="📋 已选择的文件", 
                            font=('Arial', 12, 'bold'),
                            bg='#f0f0f0', fg='#2c3e50')
        list_title.pack(anchor='w')
        
        # 创建Treeview显示文件列表
        columns = ('文件名', '识别类型', '文件路径')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=6)
        
        # 设置列标题
        self.file_tree.heading('文件名', text='文件名')
        self.file_tree.heading('识别类型', text='识别类型')
        self.file_tree.heading('文件路径', text='文件路径')
        
        # 设置列宽
        self.file_tree.column('文件名', width=300)
        self.file_tree.column('识别类型', width=100)
        self.file_tree.column('文件路径', width=400)
        
        # 添加滚动条
        tree_scroll = ttk.Scrollbar(list_frame, orient='vertical', command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=tree_scroll.set)
        
        self.file_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')
        
        # 绑定双击事件（可用于删除文件）
        self.file_tree.bind('<Double-1>', self.remove_selected_file)
    
    def identify_file_type(self, filename):
        """根据文件名识别文件类型"""
        filename_lower = filename.lower()
        
        if "阅读打卡" in filename or "阅读" in filename:
            return "阅读打卡"
        elif "课程打卡" in filename or "必打" in filename or "课程" in filename:
            return "必打课程"
        elif "破晓打卡" in filename or "破晓" in filename:
            return "破晓打卡"
        else:
            return "未识别"
    
    def select_files(self, event=None):
        """选择文件"""
        file_paths = filedialog.askopenfilenames(
            title="选择打卡数据CSV文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        
        for file_path in file_paths:
            self.add_file(file_path)
    
    def on_drop(self, event):
        """处理文件拖拽"""
        files = event.data.split()
        for file_path in files:
            file_path = file_path.strip('{}')
            if file_path.lower().endswith('.csv'):
                self.add_file(file_path)
            else:
                messagebox.showwarning("警告", f"跳过非CSV文件: {os.path.basename(file_path)}")
    
    def add_file(self, file_path):
        """添加文件到列表"""
        if not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return
        
        # 检查是否已存在
        for file_info in self.files_info:
            if file_info['path'] == file_path:
                messagebox.showinfo("提示", f"文件已存在: {os.path.basename(file_path)}")
                return
        
        # 识别文件类型
        filename = os.path.basename(file_path)
        file_type = self.identify_file_type(filename)
        
        # 添加到列表
        file_info = {
            'path': file_path,
            'name': filename,
            'type': file_type
        }
        self.files_info.append(file_info)
        
        # 更新显示
        self.update_file_list()
        
        # 更新拖拽区域状态
        if len(self.files_info) > 0:
            self.drop_zone.configure(bg='#d5f4e6')
    
    def update_file_list(self):
        """更新文件列表显示"""
        # 清空现有项目
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)
        
        # 添加文件信息
        for file_info in self.files_info:
            self.file_tree.insert('', 'end', values=(
                file_info['name'],
                file_info['type'],
                file_info['path']
            ))
    
    def remove_selected_file(self, event):
        """删除选中的文件"""
        selection = self.file_tree.selection()
        if selection:
            item = selection[0]
            values = self.file_tree.item(item, 'values')
            file_path = values[2]
            
            # 从列表中移除
            self.files_info = [f for f in self.files_info if f['path'] != file_path]
            
            # 更新显示
            self.update_file_list()
            
            # 更新拖拽区域状态
            if len(self.files_info) == 0:
                self.drop_zone.configure(bg='#e8f4fd')
    
    def clear_files(self):
        """清空所有文件"""
        self.files_info = []
        self.update_file_list()
        self.drop_zone.configure(bg='#e8f4fd')
        
        # 清空结果
        if hasattr(self, 'result_text'):
            self.result_text.delete(1.0, tk.END)
        if hasattr(self, 'save_btn'):
            self.save_btn.configure(state='disabled')
        if hasattr(self, 'output_path_var'):
            self.output_path_var.set("")
    
    def create_result_area(self):
        """创建结果显示区域"""
        result_frame = tk.Frame(self.root, bg='#f0f0f0')
        result_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        # 结果标题
        result_title = tk.Label(result_frame, text="📈 分析结果", 
                              font=('Arial', 14, 'bold'),
                              bg='#f0f0f0', fg='#2c3e50')
        result_title.pack(anchor='w')
        
        # 创建滚动文本框
        text_frame = tk.Frame(result_frame)
        text_frame.pack(fill='both', expand=True, pady=5)
        
        self.result_text = tk.Text(text_frame, wrap='word', font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 保存按钮
        save_frame = tk.Frame(result_frame, bg='#f0f0f0')
        save_frame.pack(fill='x', pady=5)
        
        self.save_btn = tk.Button(save_frame, text="💾 保存结果到文件", 
                                command=self.save_results,
                                font=('Arial', 10),
                                bg='#27ae60', fg='white',
                                state='disabled')
        self.save_btn.pack(side='left')
        
        self.output_path_var = tk.StringVar(value="")
        self.output_label = tk.Label(save_frame, textvariable=self.output_path_var,
                                   font=('Arial', 9), bg='#f0f0f0')
        self.output_label.pack(side='left', padx=10)
    
    def start_analysis(self):
        """开始分析"""
        # 检查文件
        if not self.files_info:
            messagebox.showerror("错误", "请先选择要分析的CSV文件")
            return
        
        # 检查是否有未识别的文件
        unidentified = [f for f in self.files_info if f['type'] == '未识别']
        if unidentified:
            result = messagebox.askyesno("确认", 
                f"发现{len(unidentified)}个未识别类型的文件，是否继续分析？\n"
                f"未识别文件将被跳过。")
            if not result:
                return
        
        # 在新线程中运行分析
        self.analyze_btn.configure(state='disabled')
        self.progress_bar.start()
        
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()
    
    def run_analysis(self):
        """运行数据分析"""
        try:
            self.update_status("正在分析数据...")
            
            results = []
            for file_info in self.files_info:
                if file_info['type'] == '未识别':
                    continue
                    
                self.update_status(f"正在处理 {file_info['name']}...")
                result = self.analyze_file(file_info['path'], file_info['type'])
                results.append(result)
            
            if not results:
                self.root.after(0, lambda: self.show_error("没有可分析的文件"))
                return
            
            self.update_status("正在生成图表...")
            self.generate_charts(results)
            
            self.update_status("正在生成报告...")
            report = self.generate_report(results)
            
            # 更新UI
            self.root.after(0, lambda: self.display_results(report, results))
            
        except Exception as e:
            self.root.after(0, lambda: self.show_error(str(e)))
    
    def analyze_file(self, file_path, file_type):
        """分析单个文件"""
        df = pd.read_csv(file_path, encoding='utf-8')
        max_tasks = df['完成任务数'].max()
        
        task_stats = {
            "骇浪": defaultdict(int),
            "核浪": defaultdict(int),
            "跃浪": defaultdict(int),
            "总数据": defaultdict(int)
        }
        
        for _, row in df.iterrows():
            completed_tasks = row['完成任务数']
            wave_group = self.identify_wave_group(row['用户昵称'], row['打卡昵称'])
            
            task_stats["总数据"][completed_tasks] += 1
            if wave_group != "无浪组":
                task_stats[wave_group][completed_tasks] += 1
        
        cumulative_stats = {}
        for group, counts in task_stats.items():
            cumulative_stats[group] = self.calculate_cumulative_stats(counts, max_tasks)
        
        return {
            "文件名": file_type,
            "最新一天": max_tasks,
            "累积统计": cumulative_stats,
            "处理状态": "成功"
        }
    
    def identify_wave_group(self, user_nickname, checkin_nickname):
        """识别浪组类型"""
        combined_text = f"{user_nickname or ''} {checkin_nickname or ''}"
        
        if "骇浪" in combined_text:
            return "骇浪"
        elif "核浪" in combined_text:
            return "核浪"
        elif "跃浪" in combined_text:
            return "跃浪"
        else:
            return "无浪组"
    
    def calculate_cumulative_stats(self, task_counts, max_day):
        """计算累积统计数据"""
        cumulative = {}
        for day in range(max_day, max(0, max_day-5), -1):
            cumulative[day] = sum(count for task_day, count in task_counts.items() if task_day >= day)
        return cumulative
    
    def generate_charts(self, results):
        """生成图表"""
        output_dir = "分析结果_" + datetime.now().strftime("%Y%m%d_%H%M%S")
        os.makedirs(output_dir, exist_ok=True)
        
        for result in results:
            if result["处理状态"] != "成功":
                continue
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 8))
            
            latest_day = result['最新一天']
            # 修改：只显示最新一天往前5天的数据，不包括第6天
            days = list(range(max(1, latest_day-4), latest_day+1))
            
            groups = ['骇浪', '核浪', '跃浪', '总数据']
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
            
            x = np.arange(len(days))
            width = 0.2
            
            for i, group in enumerate(groups):
                values = [result['累积统计'][group].get(day, 0) for day in days]
                bars = ax.bar(x + i * width, values, width, label=group, color=colors[i], alpha=0.8)
                
                for bar, value in zip(bars, values):
                    if value > 0:
                        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(values)*0.01,
                               f'{value}', ha='center', va='bottom', fontsize=9)
            
            ax.set_xlabel('打卡天数', fontsize=12)
            ax.set_ylabel('累积打卡人数', fontsize=12)
            ax.set_title(f'🌊 {result["文件名"]} - 各浪组累积打卡人数统计', fontsize=14, fontweight='bold')
            ax.set_xticks(x + width * 1.5)
            ax.set_xticklabels([f'第{day}天' for day in days])
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            filename = os.path.join(output_dir, f"{result['文件名']}_累积统计图.png")
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            plt.close()
        
        self.output_dir = output_dir
    
    def generate_report(self, results):
        """生成分析报告"""
        report = "🌊 浪前打卡数据累积统计报告\n"
        report += "=" * 50 + "\n\n"
        
        for result in results:
            if result["处理状态"] != "成功":
                continue
            
            report += f"📊 {result['文件名']} 累积打卡统计\n"
            report += "-" * 30 + "\n"
            report += f"最新一天：第{result['最新一天']}天\n\n"
            
            latest_day = result['最新一天']
            target_days = list(range(latest_day, max(0, latest_day-5), -1))
            
            for group_name, group_data in result['累积统计'].items():
                report += f"🌊 {group_name}:\n"
                for day in target_days:
                    count = group_data.get(day, 0)
                    if day == latest_day:
                        report += f"  第{day}天（最新）: {count}人\n"
                    else:
                        report += f"  第{day}天及以上: {count}人\n"
                report += "\n"
            
            report += "\n"
        
        return report
    
    def display_results(self, report, results):
        """显示结果"""
        self.progress_bar.stop()
        self.analyze_btn.configure(state='normal')
        self.save_btn.configure(state='normal')
        
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(1.0, report)
        
        self.output_path_var.set(f"图表已保存到: {self.output_dir}")
        self.update_status("分析完成！")
        
        messagebox.showinfo("完成", f"分析完成！\n图表已保存到: {self.output_dir}")
    
    def save_results(self):
        """保存结果到文件"""
        if hasattr(self, 'output_dir'):
            report_file = os.path.join(self.output_dir, "分析报告.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(self.result_text.get(1.0, tk.END))
            
            messagebox.showinfo("保存成功", f"报告已保存到: {report_file}")
    
    def show_error(self, error_msg):
        """显示错误"""
        self.progress_bar.stop()
        self.analyze_btn.configure(state='normal')
        self.update_status("分析失败")
        messagebox.showerror("错误", f"分析过程中出现错误：\n{error_msg}")
    
    def update_status(self, message):
        """更新状态"""
        self.root.after(0, lambda: self.progress_var.set(message))

def main():
    try:
        root = tkdnd.Tk()
    except:
        root = tk.Tk()
    
    app = CheckinAnalyzerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()


